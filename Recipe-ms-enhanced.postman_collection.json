{"info": {"name": "Recipe Management System API - Enhanced", "description": "Complete API collection for the Recipe Management System with detailed request payloads and validations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "item": [{"name": "<PERSON><PERSON><PERSON>", "description": "Comprehensive Recipe Management APIs with validation", "item": [{"name": "Create Recipe", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Grilled Salmon with Herbs", "type": "text", "description": "Recipe title (required, 3-100 characters)"}, {"key": "recipe_public_title", "value": "Easy Grilled Herb <PERSON>", "type": "text", "description": "Public recipe title (optional, 3-100 characters)"}, {"key": "recipe_description", "value": "A delicious and healthy salmon recipe with fresh herbs", "type": "text", "description": "Recipe description (optional, max 500 characters)"}, {"key": "recipe_complexity_level", "value": "medium", "type": "text", "description": "Recipe complexity level (easy, medium, hard)"}, {"key": "recipe_impression", "value": "0", "type": "text", "description": "Recipe impression count"}, {"key": "recipe_yield", "value": "4", "type": "text", "description": "Recipe yield quantity"}, {"key": "recipe_yield_unit", "value": "servings", "type": "text", "description": "Recipe yield unit"}, {"key": "recipe_total_portions", "value": "4", "type": "text", "description": "Total number of portions"}, {"key": "recipe_single_portion_size", "value": "250", "type": "text", "description": "Single portion size in grams"}, {"key": "recipe_serving_method", "value": "Plate individually", "type": "text", "description": "Method of serving the recipe"}, {"key": "recipe_placeholder", "value": "", "type": "text", "description": "Recipe placeholder information"}, {"key": "recipe_preparation_time", "value": "15", "type": "text", "description": "Preparation time in minutes (required, number)"}, {"key": "recipe_cook_time", "value": "20", "type": "text", "description": "Cooking time in minutes (required, number)"}, {"key": "recipe_total_time", "value": "35", "type": "text", "description": "Total time in minutes (auto-calculated)"}, {"key": "recipe_difficulty", "value": "medium", "type": "text", "description": "Recipe difficulty (enum: easy, medium, hard)"}, {"key": "recipe_serving_size", "value": "4", "type": "text", "description": "Number of servings (required, number)"}, {"key": "recipe_status", "value": "draft", "type": "text", "description": "Recipe status (enum: draft, published, archived)"}, {"key": "has_recipe_public_visibility", "value": "false", "type": "text", "description": "Public visibility flag (boolean)"}, {"key": "has_recipe_private_visibility", "value": "true", "type": "text", "description": "Private visibility flag (boolean)"}, {"key": "recipe_serve_in", "value": "Serve hot with lemon wedges", "type": "text", "description": "Serving instructions (optional, max 200 characters)"}, {"key": "recipe_garnish", "value": "Fresh dill and lemon slices", "type": "text", "description": "Garnish instructions (optional, max 200 characters)"}, {"key": "recipe_head_chef_tips", "value": "Ensure the grill is properly preheated for best results", "type": "text", "description": "Chef tips (optional, max 500 characters)"}, {"key": "recipe_foh_tips", "value": "Present with a wine pairing suggestion", "type": "text", "description": "Front of House tips (optional, max 500 characters)"}, {"key": "vitamin_a", "value": "120", "type": "text", "description": "Vitamin A content (in IU)"}, {"key": "vitamin_c", "value": "15", "type": "text", "description": "Vitamin C content (in mg)"}, {"key": "calcium", "value": "100", "type": "text", "description": "Calcium content (in mg)"}, {"key": "iron", "value": "2.5", "type": "text", "description": "Iron content (in mg)"}, {"key": "is_ingredient_cooking_method", "value": "true", "type": "text", "description": "Whether to include ingredient cooking method"}, {"key": "is_preparation_method", "value": "true", "type": "text", "description": "Whether to include preparation method"}, {"key": "categories", "value": "[1, 2]", "type": "text", "description": "Category IDs (required, array of numbers)"}, {"key": "nutrition_attributes", "value": "[{\"id\": 1, \"unit_of_measure\": 2, \"unit\": 25.5, \"description\": \"Protein content\"}]", "type": "text", "description": "Nutrition attributes (array of objects)"}, {"key": "allergen_attributes", "value": "[{\"id\": 1, \"description\": \"Contains nuts\"}]", "type": "text", "description": "Allergen attributes (array of objects)"}, {"key": "cuisine_attributes", "value": "[{\"id\": 1, \"description\": \"Mediterranean\"}]", "type": "text", "description": "Cuisine attributes (array of objects)"}, {"key": "dietary_attributes", "value": "[{\"id\": 1, \"description\": \"Gluten-free\"}]", "type": "text", "description": "Dietary attributes (array of objects)"}, {"key": "haccp_attributes", "value": "[{\"id\": 1, \"description\": \"Temperature control required\"}]", "type": "text", "description": "HACCP attributes (array of objects)"}, {"key": "ingredients", "value": "[{\"id\": 1, \"quantity\": 500, \"measure\": 1, \"wastage\": 5.0, \"cost\": 15.99, \"cooking_method\": 3, \"preparation_method\": 4}]", "type": "text", "description": "Recipe ingredients (required, array of objects)"}, {"key": "steps", "value": "[{\"step_number\": 1, \"description\": \"Preheat grill to medium-high heat\", \"time\": 5, \"temperature\": 200}, {\"step_number\": 2, \"description\": \"Season salmon with herbs\", \"time\": 5}]", "type": "text", "description": "Recipe steps (required, array of objects)"}, {"key": "recipe_images", "type": "file", "src": [], "description": "Recipe images (max 10 files, formats: jpg, png, max 5MB each)"}, {"key": "step_images", "type": "file", "src": [], "description": "Step images (max 20 files, formats: jpg, png, max 5MB each)"}]}, "url": {"raw": "{{baseUrl}}/private/recipes/create", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "create"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": []}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": {"status": "success", "message": "Recipe created successfully", "data": {"id": 1, "recipe_title": "Grilled Salmon with Herbs", "recipe_slug": "grilled-salmon-with-herbs", "created_at": "2025-06-18T10:00:00.000Z", "updated_at": "2025-06-18T10:00:00.000Z"}}}, {"name": "Validation Error Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": []}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": {"status": "error", "message": "Validation failed", "errors": [{"field": "recipe_title", "message": "Recipe title is required and must be between 3 and 100 characters"}, {"field": "recipe_preparation_time", "message": "Preparation time must be a positive number"}]}}]}, {"name": "Update Recipe", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Updated Grilled Salmon Recipe", "type": "text", "description": "Updated recipe title (3-100 characters)"}]}, "url": {"raw": "{{baseUrl}}/private/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "update", "{{recipeId}}"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": {"status": "success", "message": "Recipe updated successfully", "data": {"id": "{{recipeId}}", "recipe_title": "Updated Grilled Salmon Recipe", "updated_at": "2025-06-18T10:30:00.000Z"}}}]}, {"name": "Get Recipe By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/private/recipes/get-by-id/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "get-by-id", "{{recipeId}}"], "query": [{"key": "include", "value": "categories,ingredients,steps,attributes", "description": "Comma-separated list of relations to include"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": {"status": "success", "data": {"id": "{{recipeId}}", "recipe_title": "Grilled Salmon with Herbs", "recipe_description": "A delicious and healthy salmon recipe with fresh herbs", "recipe_preparation_time": 15, "recipe_cook_time": 20, "recipe_total_time": 35, "recipe_difficulty": "medium", "recipe_serving_size": 4, "recipe_status": "published", "categories": [], "ingredients": [], "steps": [], "attributes": []}}}]}, {"name": "Delete Recipe", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/private/recipes/delete/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "delete", "{{recipeId}}"]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": {"status": "success", "message": "Recipe deleted successfully"}}]}, {"name": "List Recipes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/private/recipes/list?page=1&limit=10&search=&status=published&visibility=public&sort_by=created_at&sort_order=DESC&category=&attribute=&date_from=&date_to=", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "list"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (default: 10, max: 100)"}, {"key": "search", "value": "", "description": "Search in title, description (min 3 characters)"}, {"key": "status", "value": "published", "description": "Filter by status (draft, published, archived)"}, {"key": "visibility", "value": "public", "description": "Filter by visibility (public, private)"}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, updated_at, title)"}, {"key": "sort_order", "value": "DESC", "description": "Sort order (ASC, DESC)"}, {"key": "category", "value": "", "description": "Filter by category ID"}, {"key": "attribute", "value": "", "description": "Filter by attribute ID"}, {"key": "date_from", "value": "", "description": "Filter by creation date from (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "Filter by creation date to (YYYY-MM-DD)"}]}}, "response": [{"name": "Success Response", "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": {"status": "success", "data": {"items": [], "meta": {"total": 0, "page": 1, "limit": 10, "total_pages": 0}}}}]}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1"}, {"key": "authToken", "value": "your-auth-token-here"}, {"key": "recipeId", "value": "1"}]}