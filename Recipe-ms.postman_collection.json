{"info": {"name": "Recipe Management System API", "description": "Complete API collection for the Recipe Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "item": [{"name": "Private Recipes", "item": [{"name": "Create Recipe", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Chicken Alfredo Pasta", "type": "text", "description": "Recipe title"}, {"key": "recipe_public_title", "value": "Creamy Chicken Alfredo", "type": "text", "description": "Public recipe title"}, {"key": "recipe_preparation_time", "value": "15", "type": "text", "description": "Preparation time in minutes"}, {"key": "recipe_cook_time", "value": "30", "type": "text", "description": "Cooking time in minutes"}, {"key": "has_recipe_public_visibility", "value": "false", "type": "text", "description": "Make recipe visible to public"}, {"key": "has_recipe_private_visibility", "value": "true", "type": "text", "description": "Make recipe visible to organization"}, {"key": "recipe_status", "value": "draft", "type": "text", "description": "Recipe status (draft, publish, archived, deleted)"}, {"key": "recipe_serve_in", "value": "Serve hot with garlic bread", "type": "text", "description": "Serving instructions"}, {"key": "recipe_garnish", "value": "Fresh parsley and parmesan cheese", "type": "text", "description": "Garnish instructions"}, {"key": "recipe_head_chef_tips", "value": "Use fresh cream for best results", "type": "text", "description": "Chef tips"}, {"key": "recipe_foil_tips", "value": "Cover with foil to keep warm", "type": "text", "description": "Foil tips"}, {"key": "categories", "value": "[1, 2]", "type": "text", "description": "Array of category IDs"}, {"key": "attributes", "value": "[{\"id\": 1, \"unit_of_measure\": 2, \"unit\": 500, \"description\": \"High in protein\"}]", "type": "text", "description": "Array of attribute objects"}, {"key": "ingredients", "value": "[{\"id\": 1, \"quantity\": 2.5, \"measure\": 1, \"wastage\": 5.0, \"cost\": 12.99, \"cooking_method\": 3, \"preparation_method\": 4}]", "type": "text", "description": "Array of ingredient objects"}, {"key": "steps", "value": "[{\"order\": 1, \"description\": \"Boil pasta according to package instructions\", \"item_id\": 5}]", "type": "text", "description": "Array of step objects"}, {"key": "resources", "value": "[{\"type\": \"link\", \"item_id\": \"123\", \"item_link\": \"https://example.com/recipe-video.mp4\", \"item_link_type\": \"video\"}]", "type": "text", "description": "Array of resource objects"}, {"key": "recipeFiles", "type": "file", "src": "", "description": "Recipe files (up to 10)"}, {"key": "stepImages", "type": "file", "src": "", "description": "Step images (up to 20)"}, {"key": "recipePlaceholder", "type": "file", "src": "", "description": "Recipe placeholder/thumbnail image"}]}, "url": {"raw": "{{baseUrl}}/private/recipes/create", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "create"]}, "description": "Create a new recipe with categories, attributes, ingredients, steps, resources, and file uploads"}, "response": []}, {"name": "Get Recipe By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/get-by-id/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "get-by-id", "{{recipeId}}"]}, "description": "Retrieve a single recipe by its ID or slug with all relations"}, "response": []}, {"name": "Get Recipes List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/list?page=1&limit=20&search=&visibility=public&sort_by=updated_at&sort_order=DESC", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "list"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Number of items per page"}, {"key": "search", "value": "", "description": "Search in recipe title, public title, serve in, garnish"}, {"key": "categories", "value": "", "description": "Filter by category IDs (comma-separated)", "disabled": true}, {"key": "allergens", "value": "", "description": "Filter by allergen attribute IDs (comma-separated)", "disabled": true}, {"key": "dietary", "value": "", "description": "Filter by dietary attribute IDs (comma-separated)", "disabled": true}, {"key": "cuisine", "value": "", "description": "Filter by cuisine attribute IDs (comma-separated)", "disabled": true}, {"key": "portion_cost_min", "value": "", "description": "Minimum portion cost filter", "disabled": true}, {"key": "portion_cost_max", "value": "", "description": "Maximum portion cost filter", "disabled": true}, {"key": "bookmark", "value": "", "description": "Filter by bookmark status", "disabled": true}, {"key": "visibility", "value": "public", "description": "Recipe visibility filter (public by default, private to show private recipes)"}, {"key": "sort_by", "value": "updated_at", "description": "Sort field"}, {"key": "sort_order", "value": "DESC", "description": "Sort order"}]}, "description": "Ultra-optimized recipes list with millisecond response times. Shows public recipes by default, or private recipes when visibility=private."}, "response": []}, {"name": "Update Recipe", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "recipe_title", "value": "Updated Chicken Alfredo <PERSON>", "type": "text", "description": "Recipe title"}, {"key": "recipe_public_title", "value": "Updated Creamy Chicken Alfredo", "type": "text", "description": "Public recipe title"}, {"key": "recipe_preparation_time", "value": "20", "type": "text", "description": "Preparation time in minutes"}, {"key": "recipe_cook_time", "value": "35", "type": "text", "description": "Cooking time in minutes"}, {"key": "has_recipe_public_visibility", "value": "true", "type": "text", "description": "Make recipe visible to public"}, {"key": "has_recipe_private_visibility", "value": "true", "type": "text", "description": "Make recipe visible to organization"}, {"key": "recipe_status", "value": "publish", "type": "text", "description": "Recipe status (draft, publish, archived, deleted)"}, {"key": "recipe_serve_in", "value": "Serve hot with garlic bread and salad", "type": "text", "description": "Serving instructions"}, {"key": "recipe_garnish", "value": "Fresh parsley, parmesan cheese, and black pepper", "type": "text", "description": "Garnish instructions"}, {"key": "recipe_head_chef_tips", "value": "Use fresh cream and high-quality parmesan for best results", "type": "text", "description": "Chef tips"}, {"key": "recipe_foil_tips", "value": "Cover with foil to keep warm during serving", "type": "text", "description": "Foil tips"}, {"key": "categories", "value": "[1, 2, 4]", "type": "text", "description": "Array of category IDs"}, {"key": "attributes", "value": "[{\"id\": 1, \"unit_of_measure\": 2, \"unit\": 600, \"description\": \"High in protein and calcium\"}]", "type": "text", "description": "Array of attribute objects"}, {"key": "ingredients", "value": "[{\"id\": 1, \"quantity\": 3.0, \"measure\": 1, \"wastage\": 4.0, \"cost\": 15.99, \"cooking_method\": 3, \"preparation_method\": 4}]", "type": "text", "description": "Array of ingredient objects"}, {"key": "steps", "value": "[{\"order\": 1, \"description\": \"Boil pasta in salted water according to package instructions\", \"item_id\": 5}]", "type": "text", "description": "Array of step objects"}, {"key": "resources", "value": "[{\"type\": \"link\", \"item_id\": \"456\", \"item_link\": \"https://example.com/updated-recipe-video.mp4\", \"item_link_type\": \"video\"}]", "type": "text", "description": "Array of resource objects"}, {"key": "recipeFiles", "type": "file", "src": "", "description": "Recipe files (up to 10)"}, {"key": "stepImages", "type": "file", "src": "", "description": "Step images (up to 20)"}, {"key": "recipePlaceholder", "type": "file", "src": "", "description": "Recipe placeholder/thumbnail image"}]}, "url": {"raw": "{{baseUrl}}/private/recipes/update/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "update", "{{recipeId}}"]}, "description": "Intelligently update recipe - supports both full updates and section-wise updates. Only updates provided fields, perfect for step-wise recipe building."}, "response": []}, {"name": "Archive Recipe", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/archive/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "archive", "{{recipeId}}"]}, "description": "Archives a recipe and removes all user assignments and bookmarks. Only ADMIN_SIDE_USER can archive recipes."}, "response": []}, {"name": "Delete Recipe", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/delete/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "delete", "{{recipeId}}"]}, "description": "Deletes recipe only if no users are assigned OR if recipe is archived. Only ADMIN_SIDE_USER can delete recipes."}, "response": []}, {"name": "Publish Recipe", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"has_recipe_public_visibility\": true,\n  \"has_recipe_private_visibility\": true\n}"}, "url": {"raw": "{{baseUrl}}/private/recipes/publish/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "publish", "{{recipeId}}"]}, "description": "Change recipe status from draft to published. Only use when user explicitly chooses to publish."}, "response": []}, {"name": "Make Recipe Public", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"has_recipe_public_visibility\": true,\n  \"has_recipe_private_visibility\": true\n}"}, "url": {"raw": "{{baseUrl}}/private/recipes/make-public/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "make-public", "{{recipeId}}"]}, "description": "Change recipe visibility settings to make it public or private."}, "response": []}, {"name": "Get Recipe History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/history/{{recipeId}}?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "history", "{{recipeId}}"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of items per page"}, {"key": "action", "value": "", "description": "Filter by action type", "disabled": true}, {"key": "user_id", "value": "", "description": "Filter by user ID who made the change", "disabled": true}]}, "description": "Retrieve the complete history of changes made to a recipe"}, "response": []}, {"name": "Duplicate Recipe", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/duplicate/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "duplicate", "{{recipeId}}"]}, "description": "Create a copy of an existing recipe with all its relations (categories, ingredients, steps, etc.)"}, "response": []}, {"name": "Increment Recipe Impression", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/impression/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "impression", "{{recipeId}}"]}, "description": "Increment the impression count for a recipe (can be used for both public and private recipes)"}, "response": []}, {"name": "Toggle Recipe Bookmark", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/bookmark/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "bookmark", "{{recipeId}}"]}, "description": "Add or remove a recipe bookmark for the current user"}, "response": []}, {"name": "Export Recipe", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipes/export/{{recipeId}}?format=excel", "host": ["{{baseUrl}}"], "path": ["private", "recipes", "export", "{{recipeId}}"], "query": [{"key": "format", "value": "excel", "description": "Export format (excel, csv, pdf)"}]}, "description": "Export a single recipe to Excel, CSV, or PDF format"}, "response": []}]}, {"name": "Public APIs", "item": [{"name": "Public Recipes", "item": [{"name": "Get Recipes List", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/public/recipes/list?page=1&limit=20&search=&sort_by=updated_at&sort_order=DESC", "host": ["{{baseUrl}}"], "path": ["public", "recipes", "list"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Number of items per page"}, {"key": "search", "value": "", "description": "Search in recipe title, public title, serve in, garnish"}, {"key": "categories", "value": "", "description": "Filter by category IDs (comma-separated)", "disabled": true}, {"key": "allergens", "value": "", "description": "Filter by allergen attribute IDs (comma-separated)", "disabled": true}, {"key": "dietary", "value": "", "description": "Filter by dietary attribute IDs (comma-separated)", "disabled": true}, {"key": "cuisine", "value": "", "description": "Filter by cuisine attribute IDs (comma-separated)", "disabled": true}, {"key": "portion_cost_min", "value": "", "description": "Minimum portion cost filter", "disabled": true}, {"key": "portion_cost_max", "value": "", "description": "Maximum portion cost filter", "disabled": true}, {"key": "visibility", "value": "public", "description": "Recipe visibility filter (public by default, private to show private recipes)"}, {"key": "sort_by", "value": "updated_at", "description": "Sort field"}, {"key": "sort_order", "value": "DESC", "description": "Sort order"}]}, "description": "Ultra-optimized recipes list with millisecond response times. Shows public recipes by default, or private recipes when visibility=private."}, "response": []}, {"name": "Get Recipe By ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/public/recipes/get-by-id/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["public", "recipes", "get-by-id", "{{recipeId}}"]}, "description": "Retrieve a single published public recipe by its ID or slug with all relations"}, "response": []}, {"name": "Increment Recipe Impression", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/public/recipes/impression/{{recipeId}}", "host": ["{{baseUrl}}"], "path": ["public", "recipes", "impression", "{{recipeId}}"]}, "description": "Increment the impression count for a public recipe by ID or slug (no authentication required)"}, "response": []}]}, {"name": "Public Contact", "item": [{"name": "Create Contact Us", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobile\": \"+1234567890\",\n  \"message\": \"I want to know more about your recipes\"\n}"}, "url": {"raw": "{{baseUrl}}/public/contact-us", "host": ["{{baseUrl}}"], "path": ["public", "contact-us"]}, "description": "Create new contact us submission"}, "response": []}, {"name": "Get All Contact Us", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/public/contact-us/list", "host": ["{{baseUrl}}"], "path": ["public", "contact-us", "list"]}, "description": "Get all contact us submissions"}, "response": []}, {"name": "Get Contact Us By ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/public/contact-us/get/{{contactId}}", "host": ["{{baseUrl}}"], "path": ["public", "contact-us", "get", "{{contactId}}"]}, "description": "Get single contact us by ID"}, "response": []}, {"name": "Update Contact Us", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobile\": \"+1234567890\",\n  \"message\": \"Updated message about your recipes\"\n}"}, "url": {"raw": "{{baseUrl}}/public/contact-us/update/{{contactId}}", "host": ["{{baseUrl}}"], "path": ["public", "contact-us", "update", "{{contactId}}"]}, "description": "Update contact us by ID"}, "response": []}, {"name": "Delete Contact Us", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/public/contact-us/delete/{{contactId}}", "host": ["{{baseUrl}}"], "path": ["public", "contact-us", "delete", "{{contactId}}"]}, "description": "Delete contact us by ID"}, "response": []}]}, {"name": "Public Analytics", "item": [{"name": "Track Recipe View", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"organization_id\": \"org_123\",\n  \"session_id\": \"session_abc123\",\n  \"recipe_name\": \"Chocolate Cake\",\n  \"view_duration\": 45,\n  \"referrer\": \"https://google.com\"\n}"}, "url": {"raw": "{{baseUrl}}/public/analytics/track/recipe-view", "host": ["{{baseUrl}}"], "path": ["public", "analytics", "track", "recipe-view"]}, "description": "Track recipe view (public - no auth required)"}, "response": []}, {"name": "Track CTA Click", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"organization_id\": \"org_123\",\n  \"session_id\": \"sess_abc123\",\n  \"recipe_name\": \"Chocolate Cake\",\n  \"cta_type\": \"contact_form\",\n  \"cta_text\": \"Get Recipe Details\"\n}"}, "url": {"raw": "{{baseUrl}}/public/analytics/track/cta-click", "host": ["{{baseUrl}}"], "path": ["public", "analytics", "track", "cta-click"]}, "description": "Track CTA click (simplified endpoint)"}, "response": []}, {"name": "Submit Contact Form", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipe_id\": 123,\n  \"organization_id\": \"org_123\",\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobile\": \"+1234567890\",\n  \"message\": \"I want the complete recipe for this chocolate cake!\",\n  \"recipe_name\": \"Chocolate Cake\"\n}"}, "url": {"raw": "{{baseUrl}}/public/analytics/contact-form", "host": ["{{baseUrl}}"], "path": ["public", "analytics", "contact-form"]}, "description": "Submit contact form (public - no auth required)"}, "response": []}]}]}, {"name": "Private APIs", "item": [{"name": "Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/category/list?page=1&limit=10&search=&status=&type=&categoryUse=&hasIcon=&sort=category_name&order=ASC", "host": ["{{baseUrl}}"], "path": ["private", "category", "list"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of items per page"}, {"key": "search", "value": "", "description": "Search term for category name or description"}, {"key": "status", "value": "", "description": "Filter by category status", "disabled": true}, {"key": "type", "value": "", "description": "Filter by category type", "disabled": true}, {"key": "categoryUse", "value": "", "description": "Alias for type parameter", "disabled": true}, {"key": "hasIcon", "value": "", "description": "Filter by presence of icon", "disabled": true}, {"key": "sort", "value": "category_name", "description": "Sort field"}, {"key": "order", "value": "ASC", "description": "Sort order"}]}, "description": "Retrieve all categories with advanced filtering, searching, and pagination"}, "response": []}, {"name": "Get Category By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/category/get/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["private", "category", "get", "{{categoryId}}"]}, "description": "Retrieve a single category by its ID"}, "response": []}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "category_name", "value": "Vegetables", "type": "text", "description": "Category name"}, {"key": "category_description", "value": "Fresh vegetables and greens", "type": "text", "description": "Category description"}, {"key": "category_status", "value": "active", "type": "text", "description": "Category status (active/inactive)"}, {"key": "category_type", "value": "ingredient", "type": "text", "description": "Category type (recipe/ingredient)"}, {"key": "categoryIcon", "type": "file", "src": "", "description": "Category icon image file"}]}, "url": {"raw": "{{baseUrl}}/private/category/create", "host": ["{{baseUrl}}"], "path": ["private", "category", "create"]}, "description": "Create a new category with optional icon upload"}, "response": []}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "category_name", "value": "Updated Vegetables", "type": "text", "description": "Category name"}, {"key": "category_description", "value": "Updated fresh vegetables and greens", "type": "text", "description": "Category description"}, {"key": "category_status", "value": "active", "type": "text", "description": "Category status (active/inactive)"}, {"key": "category_type", "value": "ingredient", "type": "text", "description": "Category type (recipe/ingredient)"}, {"key": "categoryIcon", "type": "file", "src": "", "description": "Category icon image file"}]}, "url": {"raw": "{{baseUrl}}/private/category/update/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["private", "category", "update", "{{categoryId}}"]}, "description": "Update an existing category with optional icon upload"}, "response": []}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/category/delete/{{categoryId}}", "host": ["{{baseUrl}}"], "path": ["private", "category", "delete", "{{categoryId}}"]}, "description": "Soft delete a category by ID"}, "response": []}]}, {"name": "Food Attributes", "item": [{"name": "Get All Food Attributes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/food-attributes/list?page=1&limit=10&search=&attribute_type=&attribute_status=&hasIcon=&sort=attribute_title&order=ASC", "host": ["{{baseUrl}}"], "path": ["private", "food-attributes", "list"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of items per page"}, {"key": "search", "value": "", "description": "Search term for attribute title or description"}, {"key": "attribute_type", "value": "", "description": "Filter by attribute type (nutrition, allergen, cuisine, dietary)", "disabled": true}, {"key": "attribute_status", "value": "", "description": "Filter by attribute status (active, inactive)", "disabled": true}, {"key": "hasIcon", "value": "", "description": "Filter by presence of icon (true, false)", "disabled": true}, {"key": "sort", "value": "attribute_title", "description": "Sort field"}, {"key": "order", "value": "ASC", "description": "Sort order"}]}, "description": "Retrieve all food attributes grouped by type (nutrition, allergen, cuisine, dietary) with filtering and pagination"}, "response": []}, {"name": "Get Food Attribute By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/food-attributes/get/{{attributeId}}", "host": ["{{baseUrl}}"], "path": ["private", "food-attributes", "get", "{{attributeId}}"]}, "description": "Retrieve a single food attribute by its ID"}, "response": []}, {"name": "Create Food Attribute", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "attribute_title", "value": "<PERSON><PERSON>", "type": "text", "description": "Attribute title"}, {"key": "attribute_description", "value": "High protein content", "type": "text", "description": "Attribute description"}, {"key": "attribute_type", "value": "nutrition", "type": "text", "description": "Attribute type (nutrition, allergen, cuisine, dietary)"}, {"key": "attribute_status", "value": "active", "type": "text", "description": "Attribute status (active, inactive)"}, {"key": "attributeIcon", "type": "file", "src": "", "description": "Attribute icon image file"}]}, "url": {"raw": "{{baseUrl}}/private/food-attributes/create", "host": ["{{baseUrl}}"], "path": ["private", "food-attributes", "create"]}, "description": "Create a new food attribute with optional icon upload"}, "response": []}, {"name": "Update Food Attribute", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "attribute_title", "value": "Updated Protein", "type": "text", "description": "Attribute title"}, {"key": "attribute_description", "value": "Updated high protein content", "type": "text", "description": "Attribute description"}, {"key": "attribute_type", "value": "nutrition", "type": "text", "description": "Attribute type (nutrition, allergen, cuisine, dietary)"}, {"key": "attribute_status", "value": "active", "type": "text", "description": "Attribute status (active, inactive)"}, {"key": "attributeIcon", "type": "file", "src": "", "description": "Attribute icon image file"}]}, "url": {"raw": "{{baseUrl}}/private/food-attributes/update/{{attributeId}}", "host": ["{{baseUrl}}"], "path": ["private", "food-attributes", "update", "{{attributeId}}"]}, "description": "Update an existing food attribute with optional icon upload"}, "response": []}, {"name": "Delete Food Attribute", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/food-attributes/delete/{{attributeId}}", "host": ["{{baseUrl}}"], "path": ["private", "food-attributes", "delete", "{{attributeId}}"]}, "description": "Soft delete a food attribute by ID"}, "response": []}]}, {"name": "Recipe Measures", "item": [{"name": "Get All Recipe Measures", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipe-measures/list?page=1&limit=10&search=&unit_status=&hasIcon=&sort=unit_title&order=ASC", "host": ["{{baseUrl}}"], "path": ["private", "recipe-measures", "list"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of items per page"}, {"key": "search", "value": "", "description": "Search term for unit title or description"}, {"key": "unit_status", "value": "", "description": "Filter by unit status (active, inactive)", "disabled": true}, {"key": "hasIcon", "value": "", "description": "Filter by presence of icon (true, false)", "disabled": true}, {"key": "sort", "value": "unit_title", "description": "Sort field"}, {"key": "order", "value": "ASC", "description": "Sort order"}]}, "description": "Retrieve all recipe measurement units with filtering, searching, and pagination"}, "response": []}, {"name": "Get Recipe Measure By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipe-measures/get/{{measureId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipe-measures", "get", "{{measureId}}"]}, "description": "Retrieve a single recipe measurement unit by its ID"}, "response": []}, {"name": "Create <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "unit_title", "value": "Grams", "type": "text", "description": "Unit title"}, {"key": "status", "value": "active", "type": "text", "description": "Unit status (active, inactive)"}, {"key": "unitIcon", "type": "file", "src": "", "description": "Unit icon image file"}]}, "url": {"raw": "{{baseUrl}}/private/recipe-measures/create", "host": ["{{baseUrl}}"], "path": ["private", "recipe-measures", "create"]}, "description": "Create a new recipe measurement unit with optional icon upload"}, "response": []}, {"name": "Update Recipe Me<PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "unit_title", "value": "Updated Grams", "type": "text", "description": "Unit title"}, {"key": "status", "value": "active", "type": "text", "description": "Unit status (active, inactive)"}, {"key": "unitIcon", "type": "file", "src": "", "description": "Unit icon image file"}]}, "url": {"raw": "{{baseUrl}}/private/recipe-measures/update/{{measureId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipe-measures", "update", "{{measureId}}"]}, "description": "Update an existing recipe measurement unit with optional icon upload"}, "response": []}, {"name": "Delete Recipe Measure", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/recipe-measures/delete/{{measureId}}", "host": ["{{baseUrl}}"], "path": ["private", "recipe-measures", "delete", "{{measureId}}"]}, "description": "Soft delete a recipe measurement unit by ID"}, "response": []}]}, {"name": "Ingredients", "item": [{"name": "Get All Ingredients", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/ingredients/get-all?page=1&limit=10&search=&ingredient_status=&category=&allergy=&cuisine=&dietary=&sort_by=name&sort_order=ASC", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "get-all"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of items per page"}, {"key": "search", "value": "", "description": "Search in ingredient name and description"}, {"key": "ingredient_status", "value": "", "description": "Filter by ingredient status", "disabled": true}, {"key": "category", "value": "", "description": "Filter by category IDs (comma-separated)", "disabled": true}, {"key": "allergy", "value": "", "description": "Filter by allergy attribute IDs (comma-separated)", "disabled": true}, {"key": "cuisine", "value": "", "description": "Filter by cuisine attribute IDs (comma-separated)", "disabled": true}, {"key": "dietary", "value": "", "description": "Filter by dietary attribute IDs (comma-separated)", "disabled": true}, {"key": "sort_by", "value": "name", "description": "Sort by field"}, {"key": "sort_order", "value": "ASC", "description": "Sort order"}]}, "description": "Retrieve all ingredients with advanced filtering, sorting, pagination, and export functionality"}, "response": []}, {"name": "Get Ingredient By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/ingredients/get-by-id/{{ingredientId}}", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "get-by-id", "{{ingredientId}}"]}, "description": "Retrieve a single ingredient by its ID or slug with all relations (categories, attributes, etc.)"}, "response": []}, {"name": "Create Ingredient", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ingredient_name\": \"Chicken Breast\",\n  \"ingredient_description\": \"Fresh organic chicken breast\",\n  \"ingredient_status\": \"active\",\n  \"cost_per_unit\": 12.99,\n  \"waste_percentage\": 5.5,\n  \"unit_of_measure\": 1,\n  \"categories\": [1, 2, 3],\n  \"nutritions\": [\n    {\n      \"id\": 1,\n      \"unit_of_measure\": 2,\n      \"unit\": 25.5\n    }\n  ],\n  \"allergens\": [4, 5],\n  \"dietary\": [6, 7]\n}"}, "url": {"raw": "{{baseUrl}}/private/ingredients/create", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "create"]}, "description": "Create a new ingredient with categories, nutritional attributes, allergens, and dietary information"}, "response": []}, {"name": "Update Ingredient", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ingredient_name\": \"Updated Chicken Breast\",\n  \"ingredient_description\": \"Updated fresh organic chicken breast\",\n  \"ingredient_status\": \"active\",\n  \"cost_per_unit\": 13.99,\n  \"waste_percentage\": 6.0,\n  \"unit_of_measure\": 1,\n  \"categories\": [1, 2, 4],\n  \"nutritions\": [\n    {\n      \"id\": 1,\n      \"unit_of_measure\": 2,\n      \"unit\": 26.0\n    }\n  ],\n  \"allergens\": [4, 5, 6],\n  \"dietary\": [7, 8]\n}"}, "url": {"raw": "{{baseUrl}}/private/ingredients/update/{{ingredientId}}", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "update", "{{ingredientId}}"]}, "description": "Update an existing ingredient with its categories, nutritional attributes, allergens, and dietary information"}, "response": []}, {"name": "Delete Ingredient", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/ingredients/delete/{{ingredientId}}", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "delete", "{{ingredientId}}"]}, "description": "Soft delete an ingredient and all its relations (categories, attributes, etc.)"}, "response": []}, {"name": "Download Import Template", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/ingredients/import-template", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "import-template"]}, "description": "Download Excel template file for ingredient bulk import"}, "response": []}, {"name": "Import Ingredients", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "", "description": "Excel file (.xlsx) containing ingredient data"}]}, "url": {"raw": "{{baseUrl}}/private/ingredients/import", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "import"]}, "description": "Bulk import ingredients from Excel file with categories, nutritional attributes, allergens, and dietary information"}, "response": []}, {"name": "Export Ingredients to Excel", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/ingredients/export/excel", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "export", "excel"]}, "description": "Export ingredients to Excel using getIngredients with download=excel"}, "response": []}, {"name": "Export Ingredients to CSV", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/ingredients/export/csv", "host": ["{{baseUrl}}"], "path": ["private", "ingredients", "export", "csv"]}, "description": "Export ingredients to CSV using getIngredients with download=csv"}, "response": []}]}, {"name": "Settings", "item": [{"name": "Get Recipe Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/settings/recipe-configuration", "host": ["{{baseUrl}}"], "path": ["private", "settings", "recipe-configuration"]}, "description": "Get recipe settings configuration"}, "response": []}, {"name": "Update Recipe Configuration", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"privateRecipeVisibilitySettings\": {\n    \"highlightChanges\": true\n  },\n  \"publicRecipeSettings\": {\n    \"publicStoreAccess\": true\n  },\n  \"publicRecipeCallToAction\": {},\n  \"recipeDetailsToDisplayPublicly\": {}\n}"}, "url": {"raw": "{{baseUrl}}/private/settings/update-recipe-configuration", "host": ["{{baseUrl}}"], "path": ["private", "settings", "update-recipe-configuration"]}, "description": "Update recipe settings configuration"}, "response": []}]}, {"name": "Dashboard", "item": [{"name": "Get Dashboard Overview", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/dashboard/overview?date_range=last_30_days", "host": ["{{baseUrl}}"], "path": ["private", "dashboard", "overview"], "query": [{"key": "date_range", "value": "last_30_days", "description": "Date range for analytics data"}]}, "description": "Get dashboard overview statistics"}, "response": []}, {"name": "Get Public Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/dashboard/public-analytics?date_range=last_30_days", "host": ["{{baseUrl}}"], "path": ["private", "dashboard", "public-analytics"], "query": [{"key": "date_range", "value": "last_30_days", "description": "Date range for analytics data"}]}, "description": "Get public recipe analytics"}, "response": []}, {"name": "Dashboard Test", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/dashboard/test", "host": ["{{baseUrl}}"], "path": ["private", "dashboard", "test"]}, "description": "Test dashboard endpoint (simple response)"}, "response": []}, {"name": "Export Dashboard Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/dashboard/export?format=json&date_range=last_30_days", "host": ["{{baseUrl}}"], "path": ["private", "dashboard", "export"], "query": [{"key": "format", "value": "json", "description": "Export format"}, {"key": "date_range", "value": "last_30_days", "description": "Date range for analytics data"}]}, "description": "Export dashboard data"}, "response": []}]}, {"name": "Analytics", "item": [{"name": "Get Analytics Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/analytics?page=1&limit=20&date_range=last_30_days", "host": ["{{baseUrl}}"], "path": ["private", "analytics"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of items per page"}, {"key": "event_type", "value": "", "description": "Filter by event type", "disabled": true}, {"key": "entity_type", "value": "", "description": "Filter by entity type", "disabled": true}, {"key": "entity_id", "value": "", "description": "Filter by specific entity ID", "disabled": true}, {"key": "date_range", "value": "last_30_days", "description": "Date range for filtering"}, {"key": "start_date", "value": "", "description": "Start date for filtering (ISO format)", "disabled": true}, {"key": "end_date", "value": "", "description": "End date for filtering (ISO format)", "disabled": true}]}, "description": "Get analytics summary"}, "response": []}, {"name": "Get CTA Click Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/analytics/cta-clicks?date_range=last_30_days&sort=desc", "host": ["{{baseUrl}}"], "path": ["private", "analytics", "cta-clicks"], "query": [{"key": "date_range", "value": "last_30_days", "description": "Date range for analytics"}, {"key": "sort", "value": "desc", "description": "Sort order by clicks"}]}, "description": "Get CTA click analytics"}, "response": []}, {"name": "Get Recipe View Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/analytics/recipe-views?date_range=last_30_days&sort=desc", "host": ["{{baseUrl}}"], "path": ["private", "analytics", "recipe-views"], "query": [{"key": "date_range", "value": "last_30_days", "description": "Date range for analytics"}, {"key": "start_date", "value": "", "description": "Start date (required when date_range=custom)", "disabled": true}, {"key": "end_date", "value": "", "description": "End date (required when date_range=custom)", "disabled": true}, {"key": "sort", "value": "desc", "description": "Sort order by views"}]}, "description": "Get recipe view analytics"}, "response": []}, {"name": "Get Contact Submissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/analytics/contact-submissions?date_range=last_30_days", "host": ["{{baseUrl}}"], "path": ["private", "analytics", "contact-submissions"], "query": [{"key": "date_range", "value": "last_30_days", "description": "Date range for analytics"}, {"key": "recipe_id", "value": "", "description": "Filter by specific recipe ID", "disabled": true}]}, "description": "Get contact form submission analytics"}, "response": []}, {"name": "Delete Contact Submission", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/analytics/contact-submissions/{{submissionId}}", "host": ["{{baseUrl}}"], "path": ["private", "analytics", "contact-submissions", "{{submissionId}}"]}, "description": "Delete contact form submission"}, "response": []}, {"name": "Analytics Test", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/private/analytics/test", "host": ["{{baseUrl}}"], "path": ["private", "analytics", "test"]}, "description": "Test analytics endpoint (simple response)"}, "response": []}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string", "description": "Base URL for the API"}, {"key": "authToken", "value": "your-auth-token-here", "type": "string", "description": "Authentication token for private APIs"}, {"key": "recipeId", "value": "1", "type": "string", "description": "Recipe ID or slug for recipe-related requests"}, {"key": "categoryId", "value": "1", "type": "string", "description": "Category ID for category-related requests"}, {"key": "attributeId", "value": "1", "type": "string", "description": "Food attribute ID for attribute-related requests"}, {"key": "measureId", "value": "1", "type": "string", "description": "Recipe measure ID for measure-related requests"}, {"key": "ingredientId", "value": "1", "type": "string", "description": "Ingredient ID for ingredient-related requests"}, {"key": "contactId", "value": "1", "type": "string", "description": "Contact ID for contact-related requests"}, {"key": "submissionId", "value": "1", "type": "string", "description": "Submission ID for contact submission-related requests"}]}